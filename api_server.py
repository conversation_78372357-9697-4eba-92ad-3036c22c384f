#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
独立的REST API服务器
提供根据需求生成测试用例的API服务
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.api_service import create_api_app

if __name__ == '__main__':
    app = create_api_app()
    
    # 从环境变量获取配置
    host = os.getenv('API_HOST', '0.0.0.0')
    port = int(os.getenv('API_PORT', 5001))
    debug = os.getenv('API_DEBUG', 'False').lower() == 'true'
    
    print(f"启动测试用例生成API服务...")
    print(f"服务地址: http://{host}:{port}")
    print(f"API端点: http://{host}:{port}/aitest/v1/generate_test_cases")
    print(f"健康检查: http://{host}:{port}/aitest/v1/health")
    
    app.run(host=host, port=port, debug=debug)
