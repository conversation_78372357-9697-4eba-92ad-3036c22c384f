#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from typing import Dict, List, Optional
from .storage import StorageManager
from .config import Config

class SystemConfigManager:
    """系统配置管理器"""
    
    def __init__(self, storage: StorageManager, config:Config):
        self.storage = storage
        self.config = config
        self.prompts_path = config.PROMPT_PATH
        
        # 确保提示词目录存在
        os.makedirs(self.prompts_path, exist_ok=True)
    
    def get_llm_config(self) -> Dict:
        """获取大模型配置"""
        return {
            'api_base': self.config.OPENAI_API_BASE,
            'api_key': self.config.OPENAI_API_KEY,
            'model': self.config.OPENAI_MODEL
        }
    
    def update_llm_config(self, api_base: str, api_key: str, model: str) -> bool:
        """更新大模型配置"""
        try:
            self.config.update_config(
                OPENAI_API_BASE=api_base,
                OPENAI_API_KEY=api_key,
                OPENAI_MODEL=model
            )
            self.config.save_to_env()
            return True
        except Exception as e:
            print(f"更新大模型配置失败: {e}")
            return False
    
    def get_embedding_config(self) -> Dict:
        """获取嵌入模型配置"""
        return {
            'api_base': self.config.EMBEDDING_API_BASE,
            'api_key': self.config.EMBEDDING_API_KEY,
            'model': self.config.EMBEDDING_MODEL
        }
    
    def update_embedding_config(self, api_base: str, api_key: str, model: str) -> bool:
        """更新嵌入模型配置"""
        try:
            self.config.update_config(
                EMBEDDING_API_BASE=api_base,
                EMBEDDING_API_KEY=api_key,
                EMBEDDING_MODEL=model
            )
            self.config.save_to_env()
            return True
        except Exception as e:
            print(f"更新嵌入模型配置失败: {e}")
            return False
    
    def get_storage_config(self) -> Dict:
        """获取存储配置"""
        return {
            'storage_type': self.config.STORAGE_TYPE,
            'data_path': self.config.DATA_PATH,
            'upload_folder': self.config.UPLOAD_FOLDER,
            'knowledge_base_path': self.config.KNOWLEDGE_BASE_PATH,
            'chunk_size': self.config.CHUNK_SIZE,
            'chunk_overlap': self.config.CHUNK_OVERLAP
        }
    
    def update_storage_config(self, storage_type: str = None, data_path: str = None,
                            upload_folder: str = None, knowledge_base_path: str = None,
                            chunk_size: int = None, chunk_overlap: int = None) -> bool:
        """更新存储配置"""
        try:
            update_data = {}
            if storage_type is not None:
                update_data['STORAGE_TYPE'] = storage_type
            if data_path is not None:
                update_data['DATA_PATH'] = data_path
            if upload_folder is not None:
                update_data['UPLOAD_FOLDER'] = upload_folder
            if knowledge_base_path is not None:
                update_data['KNOWLEDGE_BASE_PATH'] = knowledge_base_path
            if chunk_size is not None:
                update_data['CHUNK_SIZE'] = chunk_size
            if chunk_overlap is not None:
                update_data['CHUNK_OVERLAP'] = chunk_overlap
            
            if update_data:
                self.config.update_config(**update_data)
                self.config.save_to_env()
            
            return True
        except Exception as e:
            print(f"更新存储配置失败: {e}")
            return False
    
    def list_prompt_files(self) -> List[Dict]:
        """获取提示词文件列表"""
        prompt_files = []
        
        try:
            for filename in os.listdir(self.prompts_path):
                if filename.endswith('.md'):
                    file_path = os.path.join(self.prompts_path, filename)
                    
                    # 获取文件信息
                    stat = os.stat(file_path)
                    
                    prompt_files.append({
                        'filename': filename,
                        'name': filename.replace('.md', '').replace('_', ' ').title(),
                        'size': stat.st_size,
                        'modified_at': stat.st_mtime
                    })
        except Exception as e:
            print(f"获取提示词文件列表失败: {e}")
        
        return prompt_files
    
    def get_prompt_content(self, filename: str) -> Optional[str]:
        """获取提示词内容"""
        file_path = os.path.join(self.prompts_path, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"读取提示词文件失败: {e}")
            return None
    
    def update_prompt_content(self, filename: str, content: str) -> bool:
        """更新提示词内容"""
        file_path = os.path.join(self.prompts_path, filename)

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"更新提示词文件失败: {e}")
            return False

    def get_test_case_types(self) -> Dict:
        """获取测试用例类型配置"""
        return self.config.TEST_CASE_TYPES
    
    def test_llm_connection(self) -> Dict:
        """测试大模型连接"""
        try:
            from .utils.llm_client import LLMClient
            llm_client = LLMClient(self.config)
            
            # 发送测试请求
            messages = [
                {"role": "user", "content": "请回复'连接正常'"}
            ]
            
            import time
            start_time = time.time()
            response = llm_client.chat_completion(messages, max_tokens=10)
            end_time = time.time()
            
            return {
                'success': True,
                'response': response,
                'response_time': end_time - start_time,
                'message': '大模型连接正常'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '大模型连接失败'
            }
    
    def test_embedding_connection(self) -> Dict:
        """测试嵌入模型连接"""
        try:
            from .utils.embedding_client import EmbeddingClient
            embedding_client = EmbeddingClient(self.config)
            
            return embedding_client.test_embedding_service()
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '嵌入模型连接失败'
            }
    
    def get_system_info(self) -> Dict:
        """获取系统信息"""
        import platform
        import psutil
        
        try:
            return {
                'platform': platform.platform(),
                'python_version': platform.python_version(),
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total,
                'memory_available': psutil.virtual_memory().available,
                'disk_usage': psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent
            }
        except Exception as e:
            return {
                'error': str(e),
                'message': '获取系统信息失败'
            }

    def get_all_config(self) -> Dict:
        """获取所有配置"""
        return self.config.get_all()

    def update_all_config(self, config_data: Dict) -> bool:
        """更新所有配置"""
        try:
            # 更新配置对象
            for key, value in config_data.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)

            # 保存到环境文件
            self.config.save_to_env()
            return True
        except Exception as e:
            print(f"更新配置失败: {e}")
            return False

    def test_llm_connection(self) -> Dict:
        """测试LLM连接"""
        try:
            from .llm_client import LLMClient
            llm_client = LLMClient(self.config)

            # 发送一个简单的测试请求
            response = llm_client.chat("Hello", "You are a helpful assistant. Please respond with 'Connection successful'.")

            if response and "successful" in response.lower():
                return {
                    'success': True,
                    'message': 'LLM连接测试成功'
                }
            else:
                return {
                    'success': False,
                    'message': 'LLM响应异常'
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'LLM连接失败: {str(e)}'
            }

    def test_embedding_connection(self) -> Dict:
        """测试嵌入模型连接"""
        try:
            from .embedding_client import EmbeddingClient
            embedding_client = EmbeddingClient(self.config)

            # 发送一个简单的测试请求
            embeddings = embedding_client.get_embeddings(["test connection"])

            if embeddings and len(embeddings) > 0 and len(embeddings[0]) > 0:
                return {
                    'success': True,
                    'message': '嵌入模型连接测试成功'
                }
            else:
                return {
                    'success': False,
                    'message': '嵌入模型响应异常'
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'嵌入模型连接失败: {str(e)}'
            }
