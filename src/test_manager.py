#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import math
from .storage import StorageManager
from .utils.llm_client import LLMClient
from .config import Config
from .knowledge_manager import KnowledgeManager

class TestManager:
    """测试管理器"""
    
    def __init__(self, storage: StorageManager, config:Config):
        self.storage = storage
        self.config = config
        self.llm_client = LLMClient(config)
        self.prompts_path = config.PROMPT_PATH
        
        # 确保提示词目录存在
        os.makedirs(self.prompts_path, exist_ok=True)
        
        # 初始化默认提示词文件
        self.knowledge_manager = KnowledgeManager(self.storage, self.config)
    
    def generate_test_cases_from_requirement_ids(self, project_id: str, requirement_ids: List[str],
                                            test_types: List[str] = None, use_knowledge_base: bool = True,
                                            max_threads: int = 4, progress_callback=None) -> Dict:
        """根据需求功能点生成测试用例"""
        if max_threads < 1:
            max_threads = self.config.MAX_THREADS

        # 如果没有指定测试类型，默认生成功能测试
        if not test_types:
            test_types = ['functional']

        # 获取需求信息
        requirements = []
        for req_id in requirement_ids:
            req = self.storage.read('requirements', req_id, project_id)
            if req:
                requirements.append(req)
        
        return self.generate_test_cases_from_requirements(project_id , requirements, 
                                                           test_types, use_knowledge_base, max_threads,progress_callback)
    
    def generate_test_cases_from_requirements(self, project_id: str, requirements: List[any],
                                            test_types: List[str] = None, use_knowledge_base: bool = True,
                                            max_threads: int = 4, progress_callback=None) -> Dict:

        if not requirements:
            raise ValueError("没有找到有效的需求功能点")

        try:
            all_test_cases = []
            all_errors = []

            # 为每种测试类型生成测试用例
            for test_type in test_types:
                # 读取对应类型的提示词
                system_prompt = self.load_test_case_prompt_by_type(test_type)

                # 多线程并发处理
                result = self._generate_test_cases_multithreaded(use_knowledge_base,
                    requirements, system_prompt, project_id, max_threads, progress_callback, test_type
                )

                if result['success'] and result['test_cases']:
                    all_test_cases.extend(result['test_cases'])

                if result.get('errors'):
                    all_errors.extend(result['errors'])

            # 自动保存生成的测试用例
            saved_ids = []
            if all_test_cases:
                try:
                    saved_ids = self.save_generated_test_cases(all_test_cases)
                    print(f"自动保存了 {len(saved_ids)} 个测试用例")
                except Exception as e:
                    print(f"自动保存测试用例失败: {e}")
                    all_errors.append(f"自动保存失败: {str(e)}")

            return {
                'success': len(all_test_cases) > 0,
                'test_cases': all_test_cases,
                'saved_ids': saved_ids,
                'saved_count': len(saved_ids),
                'errors': all_errors,
                'total_generated': len(all_test_cases)
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'test_cases': [],
                'errors': [str(e)]
            }
    
    def save_generated_test_cases(self, test_cases: List[Dict]) -> List[str]:
        """保存生成的测试用例"""
        saved_ids = []
        
        for test_case in test_cases:
            try:
                # 为生成的测试用例添加AI标记
                test_case['created_at'] = datetime.now().isoformat()
                test_case['updated_at'] = datetime.now().isoformat()
                test_case['ai_generated'] = True
                test_case['ai_modified_at'] = None

                case_id = self.storage.create('test_cases', test_case, test_case['project_id'])
                saved_ids.append(case_id)
            except Exception as e:
                print(f"保存测试用例失败: {e}")
        
        return saved_ids
    
    def create_test_case(self, project_id: str, data: Dict) -> str:
        """创建测试用例"""
        data['project_id'] = project_id
        data['status'] = data.get('status', '待执行')
        data['type'] = data.get('type', '用户测试用例')
        data['test_type'] = data.get('test_type', 'functional')
        data['priority'] = data.get('priority', '中')
        data['created_at'] = datetime.now().isoformat()
        data['updated_at'] = datetime.now().isoformat()
        data['ai_generated'] = data.get('ai_generated', False)

        return self.storage.create('test_cases', data, project_id)
    
    def get_test_case(self, test_case_id: str, project_id: str = None) -> Optional[Dict]:
        """获取测试用例详情"""
        test_case = self.storage.read('test_cases', test_case_id, project_id)
        if test_case:
            # 为测试用例添加需求信息
            self._enrich_test_cases_with_requirements([test_case], project_id)
        return test_case
    
    def update_test_case(self, test_case_id: str, data: Dict, project_id: str = None) -> bool:
        """更新测试用例"""
        data['updated_at'] = datetime.now().isoformat()
        # 如果是AI生成的测试用例被修改，记录修改时间
        existing_case = self.storage.read('test_cases', test_case_id, project_id)
        if existing_case and existing_case.get('ai_generated') and not existing_case.get('ai_modified_at'):
            data['ai_modified_at'] = datetime.now().isoformat()

        return self.storage.update('test_cases', test_case_id, data, project_id)
    
    def delete_test_case(self, test_case_id: str, project_id: str = None) -> bool:
        """删除测试用例"""
        return self.storage.delete('test_cases', test_case_id, project_id)
    
    def list_test_cases(self, project_id: str, page: int = 1, page_size: int = 20,
                       filters: Dict = None, search: str = None) -> Dict:
        """获取测试用例列表"""
        if search:
            # 搜索测试用例
            test_cases = self.storage.search('test_cases', search, project_id,
                                           fields=['name', 'description', 'steps'])

            # 应用过滤器
            if filters:
                filtered_cases = []
                for case in test_cases:
                    match = True
                    for key, value in filters.items():
                        if value and case.get(key) != value:
                            match = False
                            break
                    if match:
                        filtered_cases.append(case)
                test_cases = filtered_cases

            total = len(test_cases)
            start = (page - 1) * page_size
            end = start + page_size
            page_cases = test_cases[start:end]

            # 为每个测试用例添加需求信息
            self._enrich_test_cases_with_requirements(page_cases, project_id)

            return {
                'records': page_cases,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
        else:
            result = self.storage.list('test_cases', project_id=project_id,
                                     filters=filters, page=page, page_size=page_size)

            # 为每个测试用例添加需求信息
            if result and 'records' in result:
                self._enrich_test_cases_with_requirements(result['records'], project_id)

            return result

    def list_test_cases_by_requirement(self, project_id: str, requirement_id: str,
                                     page: int = 1, page_size: int = 20) -> Dict:
        """根据功能点ID获取测试用例列表"""
        # 获取所有测试用例
        all_test_cases = self.storage.list('test_cases', project_id=project_id, page_size=10000)['records']

        # 过滤出与指定功能点关联的测试用例
        filtered_cases = []
        for case in all_test_cases:
            requirement_ids = case.get('requirement_ids', [])
            if isinstance(requirement_ids, str):
                # 兼容旧格式
                requirement_ids = [requirement_ids] if requirement_ids else []

            if requirement_id in requirement_ids:
                filtered_cases.append(case)

        # 分页
        total = len(filtered_cases)
        start = (page - 1) * page_size
        end = start + page_size
        page_cases = filtered_cases[start:end]

        result = {
            'records': page_cases,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        }

        # 为测试用例添加需求信息
        self._enrich_test_cases_with_requirements(result['records'], project_id)

        return result

    def _enrich_test_cases_with_requirements(self, test_cases: List[Dict], project_id: str):
        """为测试用例添加需求信息"""
        try:
            # 获取所有需求
            requirements_result = self.storage.list('requirements', project_id=project_id, page_size=1000)
            requirements = requirements_result.get('records', [])

            # 创建需求ID到需求信息的映射
            req_map = {}
            for req in requirements:
                req_map[req['id']] = req

            # 为每个测试用例添加需求信息
            for test_case in test_cases:
                requirement_ids = test_case.get('requirement_ids', [])
                if requirement_ids:
                    # 获取关联的需求名称
                    req_names = []
                    for req_id in requirement_ids:
                        if req_id in req_map:
                            req = req_map[req_id]
                            name = req.get('title') or req.get('description', '未命名需求')[:30]
                            req_names.append(name)

                    if req_names:
                        test_case['requirement_name'] = ', '.join(req_names)
                    else:
                        test_case['requirement_name'] = '无关联需求'
                else:
                    test_case['requirement_name'] = '无关联需求'

        except Exception as e:
            print(f"获取需求信息失败: {e}")
            # 如果获取需求信息失败，设置默认值
            for test_case in test_cases:
                test_case['requirement_name'] = '无关联需求'

    def batch_delete_test_cases(self, test_case_ids: List[str], project_id: str) -> Dict:
        """批量删除测试用例"""
        results = {
            'success_count': 0,
            'failed_count': 0,
            'errors': []
        }
        
        for case_id in test_case_ids:
            try:
                success = self.delete_test_case(case_id, project_id)
                if success:
                    results['success_count'] += 1
                else:
                    results['failed_count'] += 1
                    results['errors'].append(f"测试用例 {case_id} 删除失败")
            except Exception as e:
                results['failed_count'] += 1
                results['errors'].append(f"测试用例 {case_id} 删除失败: {str(e)}")
        
        return results
    
    def get_test_case_types(self) -> List[str]:
        """获取测试用例类型列表"""
        return ['用户测试用例', '自动化测试用例']
    
    def get_test_case_statuses(self) -> List[str]:
        """获取测试用例状态列表"""
        return ['待执行', '执行中', '通过', '失败', '阻塞']
    
    def get_test_case_priorities(self) -> List[str]:
        """获取测试用例优先级列表"""
        return ['高', '中', '低']
    
    def load_test_case_prompt(self) -> str:
        """加载测试用例生成提示词"""
        prompt_file = os.path.join(self.prompts_path, self.config.PROMPT_USE_CASE_FILE)
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception:
            raise FileNotFoundError(f"未找到提示词文件: {self.config.PROMPT_USE_CASE_FILE}")

    def load_test_case_prompt_by_type(self, test_type: str) -> str:
        """根据测试类型加载对应的提示词"""
        # 获取测试类型配置
        test_types_config = self.config.TEST_CASE_TYPES

        if test_type == 'functional':
            # 功能测试使用默认提示词
            return self.load_test_case_prompt()
        elif test_type in test_types_config:
            # 使用特定类型的提示词
            prompt_file = os.path.join(self.prompts_path, test_types_config[test_type]['prompt_file'])
            try:
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception:
                # 如果找不到特定类型的提示词，回退到默认提示词
                print(f"警告：未找到 {test_type} 类型的提示词文件，使用默认提示词")
                return self.load_test_case_prompt()
        else:
            # 未知类型，使用默认提示词
            print(f"警告：未知的测试类型 {test_type}，使用默认提示词")
            return self.load_test_case_prompt()
    
    def load_automation_prompt(self) -> str:
        """加载自动化测试用例生成提示词"""
        prompt_file = os.path.join(self.prompts_path, self.config.PROMPT_AUTOMATION_CASE_FILE)
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception:
            raise FileNotFoundError(f"未找到提示词文件: {self.config.PROMPT_AUTOMATION_CASE_FILE}")
    
    def update_test_case_prompt(self, prompt: str) -> bool:
        """更新测试用例生成提示词"""
        prompt_file = os.path.join(self.prompts_path, self.config.PROMPT_USE_CASE_FILE)
        try:
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(prompt)
            return True
        except Exception as e:
            print(f"更新提示词失败: {e}")
            return False
    
    def update_automation_prompt(self, prompt: str) -> bool:
        """更新自动化测试用例生成提示词"""
        prompt_file = os.path.join(self.prompts_path, self.config.PROMPT_AUTOMATION_CASE_FILE)
        try:
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(prompt)
            return True
        except Exception as e:
            print(f"更新提示词失败: {e}")
            return False

    def get_test_cases_by_requirement(self, requirement_id: str, project_id: str) -> List[Dict]:
        """获取指定需求的测试用例"""
        all_cases = self.storage.list('test_cases', project_id=project_id, page_size=1000)['records']

        related_cases = []
        for case in all_cases:
            requirement_ids = case.get('requirement_ids', [])
            if isinstance(requirement_ids, list) and requirement_id in requirement_ids:
                related_cases.append(case)
            elif isinstance(requirement_ids, str) and requirement_ids == requirement_id:
                related_cases.append(case)

        return related_cases

    def get_test_case_statistics(self, project_id: str) -> Dict:
        """获取测试用例统计信息"""
        all_cases = self.storage.list('test_cases', project_id=project_id, page_size=1000)['records']

        stats = {
            'total': len(all_cases),
            'by_type': {},
            'by_status': {},
            'by_priority': {}
        }

        for case in all_cases:
            # 按类型统计
            case_type = case.get('type', '未分类')
            stats['by_type'][case_type] = stats['by_type'].get(case_type, 0) + 1

            # 按状态统计
            status = case.get('status', '未设置')
            stats['by_status'][status] = stats['by_status'].get(status, 0) + 1

            # 按优先级统计
            priority = case.get('priority', '未设置')
            stats['by_priority'][priority] = stats['by_priority'].get(priority, 0) + 1

        return stats

    def execute_test_case(self, test_case_id: str, project_id: str,
                         execution_result: Dict) -> bool:
        """执行测试用例并记录结果"""
        try:
            # 更新测试用例状态和执行结果
            update_data = {
                'status': execution_result.get('status', '执行中'),
                'execution_result': execution_result.get('result', ''),
                'execution_notes': execution_result.get('notes', ''),
                'executed_at': execution_result.get('executed_at'),
                'executed_by': execution_result.get('executed_by', '')
            }

            return self.storage.update('test_cases', test_case_id, update_data, project_id)

        except Exception as e:
            print(f"记录测试执行结果失败: {e}")
            return False

    def validate_test_case_name(self, name: str, project_id: str, test_case_id: str = None) -> Dict:
        """验证测试用例名称"""
        if not name or not name.strip():
            return {'valid': False, 'message': '测试用例名称不能为空'}

        if len(name.strip()) > 200:
            return {'valid': False, 'message': '测试用例名称不能超过200个字符'}

        # 检查名称是否重复
        test_cases = self.storage.list('test_cases', project_id=project_id, page_size=1000)['records']
        for case in test_cases:
            if case['name'] == name.strip() and case['id'] != test_case_id:
                return {'valid': False, 'message': '测试用例名称已存在'}

        return {'valid': True, 'message': '测试用例名称可用'}

    def _generate_test_cases_multithreaded(self, use_knowledge_base, requirements: List[Dict],
                                         system_prompt: str, project_id: str, max_threads: int,
                                         progress_callback=None, test_type: str = 'functional') -> Dict:
        """多线程生成测试用例"""

        all_test_cases = []
        errors = []
        results_lock = threading.Lock()

        # 将需求分组，每个线程处理一组，每组一个功能点
        chunk_size = 1 #max(1, math.ceil(len(requirements) / max_threads))
        requirement_chunks = [requirements[i]
                            for i in range(0, len(requirements), chunk_size)]
        chunk_index = [0]

        if progress_callback:
            progress_callback(5)
        def process_requirement_chunk(req):
            """处理一组需求"""
            try:
                # 根据名称、描述、业务流程构建搜索查询 
                query = f"{req.get('title', '')};{req.get('spec', req.get('description', ''))}; {req.get('businessProcess', '')}"
                #print(f'[线程] 开始为 {len(chunk)} 个需求生成测试用例')
                # 获取知识库上下文
                knowledge_context = ""
                if use_knowledge_base:
                    try:
                        # 搜索相关知识
                        results = self.knowledge_manager.search_knowledge_base(project_id, query, top_k=3)
                        
                        # 构建知识库上下文
                        if results:
                            knowledge_context = '\n\n'.join([result['content'] for result in results])
                        
                    except Exception as e:
                        print(f"获取知识库上下文失败: {e}")

                # 为需求生成测试用例
                # 构建包含api_req.json中所有字段的需求描述
                req_desc = f"需求名称: {req.get('title', '')}\n"
                req_desc += f"需求描述: {req.get('spec', req.get('description', ''))}\n"
                req_desc += f"业务流程: {req.get('businessProcess', '')}\n"
                req_desc += f"输入/输出约束: {req.get('constraints', '')}\n"
                req_desc += f"验收标准: {req.get('verify', '')}\n"
                req_desc += f"备注: {req.get('comment', '')}\n"
                req_desc += f"优先级: {req.get('priority', '')}\n"
                req_desc += f"类型: {req.get('type', '')}\n"
                test_cases = self.llm_client.generate_test_cases(
                    req_desc, knowledge_context, system_prompt
                )

                # 为每个测试用例添加项目和需求关联信息
                requirement_ids = [req['id'] for req in chunk]
                for test_case in test_cases:
                    test_case['project_id'] = project_id
                    test_case['requirement_ids'] = requirement_ids
                    test_case['status'] = '待执行'
                    test_case['type'] = test_case.get('type', 'functional')
                    test_case['test_type'] = test_type

                    # 确保新字段存在，如果没有则使用默认值或从旧字段映射
                    if not test_case.get('precondition'):
                        test_case['precondition'] = test_case.get('preconditions', '')
                    if not test_case.get('stepsJson'):
                        steps = test_case.get('steps', [])
                        if isinstance(steps, list):
                            test_case['stepsJson'] = str(steps)
                        else:
                            test_case['stepsJson'] = str([steps]) if steps else '[]'
                    if not test_case.get('expectsJson'):
                        expected = test_case.get('expected_result', '')
                        test_case['expectsJson'] = str([expected]) if expected else '[]'
                    if not test_case.get('keywords'):
                        test_case['keywords'] = ''
                    if not test_case.get('pri'):
                        priority_map = {'高': '1', '中': '2', '低': '3'}
                        test_case['pri'] = priority_map.get(test_case.get('priority', '中'), '2')
                    if not test_case.get('auto'):
                        test_case['auto'] = 'no'
                    if not test_case.get('distinguish'):
                        test_case['distinguish'] = '0'
                    if not test_case.get('executionHours'):
                        test_case['executionHours'] = '1'
                    if not test_case.get('stage'):
                        test_case['stage'] = '功能测试阶段'
                    if not test_case.get('req_number'):
                        # 从关联的需求中获取需求编号
                        if chunk:
                            test_case['req_number'] = chunk[0].get('number', '')

                print(f'[线程] 已生成 {len(test_cases)} 个测试用例')
                # 更新进度
                if progress_callback:
                    with results_lock:
                        progress = 5 + int(80 * (chunk_index[0] + 1) / len(requirement_chunks))
                        progress_callback(progress)
                        chunk_index[0] += 1

                return {
                    'success': True,
                    'test_cases': test_cases
                }

            except Exception as e:
                return {
                    'success': False,
                    'error': f"处理需求组失败: {str(e)}"
                }

        # 使用线程池执行
        print(f"使用 {max_threads} 个线程并发处理 {len(requirement_chunks)} 组需求")

        with ThreadPoolExecutor(max_workers=max_threads) as executor:
            # 提交所有任务
            future_to_chunk = {executor.submit(process_requirement_chunk, chunk): chunk
                             for chunk in requirement_chunks}

            # 收集结果
            for future in as_completed(future_to_chunk):
                chunk = future_to_chunk[future]
                try:
                    result = future.result()

                    with results_lock:
                        if result['success']:
                            all_test_cases.extend(result['test_cases'])
                        else:
                            errors.append(result['error'])

                except Exception as e:
                    with results_lock:
                        errors.append(f"处理需求组异常: {str(e)}")

        print(f"多线程处理完成，共生成 {len(all_test_cases)} 个测试用例")

        return {
            'success': True,
            'test_cases': all_test_cases,
            'total_generated': len(all_test_cases),
            'errors': errors
        }

    def generate_automation_test_cases(self, project_id: str, user_test_case_ids: List[str], max_threads: int=0,
                                         progress_callback=None) -> Dict:
        """根据用户测试用例生成自动化测试用例"""
        if max_threads < 1:
            max_threads = self.config.MAX_THREADS
        try:
            # 获取用户测试用例，只有功能测试类型的用例才能生成自动化测试用例
            user_test_cases = []
            for case_id in user_test_case_ids:
                case = self.storage.read('test_cases', case_id, project_id)
                # 检查是否为功能测试类型
                if case and case.get('test_type') == 'functional':
                    user_test_cases.append(case)

            if not user_test_cases:
                return {
                    'success': False,
                    'error': '没有找到有效的功能测试用例，只有功能测试类型的用例才能生成自动化测试用例',
                    'automation_cases': []
                }

            # 将用例分组，每个线程处理一个用例
            group_size = 1 #max(1, math.ceil(len(user_test_cases) / max_threads))
            user_case_chunks = [user_test_cases[i:i + group_size]
                                for i in range(0, len(user_test_cases), group_size)]
            # 使用线程池执行
            results_lock = threading.Lock()
            print(f"使用 {max_threads} 个线程并发处理 {len(user_case_chunks)} 组用例")
        
            # 加载自动化测试生成提示词
            system_prompt = self.load_automation_prompt()

            completed_index = [0]
            if progress_callback:
                progress_callback(5)
            
            # 使用LLM生成自动化测试用例
            automation_cases = []
            with ThreadPoolExecutor(max_workers=max_threads) as executor:
                # 提交所有任务
                future_to_chunk = {executor.submit(self.llm_client.generate_automation_test_cases, chunk[0], system_prompt): chunk for chunk in user_case_chunks}

                # 收集结果
                for future in as_completed(future_to_chunk):
                    chunk = future_to_chunk[future][0]
                    try:
                        result = future.result()

                        with results_lock:
                            automation_case = {
                                'id': str(uuid.uuid4()),
                                'project_id': project_id,
                                'user_test_case_id': chunk['id'],
                                'name': f"自动化_{chunk['name']}",
                                'description': chunk.get('description', ''),
                                'test_script': result,
                                'framework': 'midscenejs',
                                'status': '待执行',
                                'created_at': datetime.now().isoformat(),
                                'updated_at': datetime.now().isoformat(),
                                'ai_generated': True,
                                'ai_modified_at': None
                            }
                            automation_cases.append(automation_case)
                            if progress_callback:
                                # 更新进度
                                progress = 5 + int(80 * (completed_index[0] + 1) / len(user_case_chunks))
                                progress_callback(progress)
                                completed_index[0] += 1

                    except Exception as e:
                        with results_lock:
                            print(f"多线程处理自动化用例组异常: {str(e)}")

            print(f"多线程处理完成，共生成 {len(automation_cases)} 个自动化用例")
            

            if progress_callback:
                progress_callback(90)
            # 保存自动化测试用例
            saved_ids = []
            for case in automation_cases:
                try:
                    case_id = self.storage.create('automation_test_cases', case, project_id)
                    saved_ids.append(case_id)
                except Exception as e:
                    print(f"保存自动化测试用例失败: {e}")

            return {
                'success': True,
                'automation_cases': automation_cases,
                'total_generated': len(automation_cases),
                'saved_ids': saved_ids
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'automation_cases': []
            }

    # 自动化测试用例管理方法
    def create_automation_test_case(self, project_id: str, data: Dict) -> str:
        """创建自动化测试用例"""
        data['project_id'] = project_id
        data['status'] = data.get('status', '待执行')
        data['framework'] = data.get('framework', 'midscenejs')
        data['created_at'] = datetime.now().isoformat()
        data['updated_at'] = datetime.now().isoformat()
        data['ai_generated'] = data.get('ai_generated', False)

        return self.storage.create('automation_test_cases', data, project_id)

    def get_automation_test_case(self, case_id: str, project_id: str = None) -> Optional[Dict]:
        """获取自动化测试用例详情"""
        automation_case = self.storage.read('automation_test_cases', case_id, project_id)
        if automation_case:
            # 添加关联的用户测试用例信息
            user_case_id = automation_case.get('user_test_case_id')
            if user_case_id:
                user_case = self.storage.read('test_cases', user_case_id, project_id)
                if user_case:
                    automation_case['user_test_case'] = user_case
        return automation_case

    def update_automation_test_case(self, case_id: str, data: Dict, project_id: str = None) -> bool:
        """更新自动化测试用例"""
        data['updated_at'] = datetime.now().isoformat()
        # 如果是AI生成的用例被修改，记录修改时间
        existing_case = self.storage.read('automation_test_cases', case_id, project_id)
        if existing_case and existing_case.get('ai_generated') and not existing_case.get('ai_modified_at'):
            data['ai_modified_at'] = datetime.now().isoformat()

        return self.storage.update('automation_test_cases', case_id, data, project_id)

    def delete_automation_test_case(self, case_id: str, project_id: str = None) -> bool:
        """删除自动化测试用例"""
        return self.storage.delete('automation_test_cases', case_id, project_id)

    def list_automation_test_cases(self, project_id: str, page: int = 1, page_size: int = 20,
                                 filters: Dict = None, search: str = None) -> Dict:
        """获取自动化测试用例列表"""
        if search:
            # 搜索自动化测试用例
            automation_cases = self.storage.search('automation_test_cases', search, project_id,
                                                 fields=['name', 'description', 'test_script'])

            # 应用过滤器
            if filters:
                filtered_cases = []
                for case in automation_cases:
                    match = True
                    for key, value in filters.items():
                        if value and case.get(key) != value:
                            match = False
                            break
                    if match:
                        filtered_cases.append(case)
                automation_cases = filtered_cases

            total = len(automation_cases)
            start = (page - 1) * page_size
            end = start + page_size
            page_cases = automation_cases[start:end]

            # 为每个自动化测试用例添加用户测试用例信息
            self._enrich_automation_cases_with_user_cases(page_cases, project_id)

            return {
                'records': page_cases,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
        else:
            result = self.storage.list('automation_test_cases', project_id=project_id,
                                     filters=filters, page=page, page_size=page_size)

            # 为每个自动化测试用例添加用户测试用例信息
            if result and 'records' in result:
                self._enrich_automation_cases_with_user_cases(result['records'], project_id)

            return result

    def _enrich_automation_cases_with_user_cases(self, automation_cases: List[Dict], project_id: str):
        """为自动化测试用例添加用户测试用例信息"""
        try:
            # 获取所有用户测试用例
            user_cases_result = self.storage.list('test_cases', project_id=project_id, page_size=1000)
            user_cases = user_cases_result.get('records', [])

            # 创建用户测试用例ID到信息的映射
            user_case_map = {}
            for case in user_cases:
                user_case_map[case['id']] = case

            # 为每个自动化测试用例添加用户测试用例信息
            for automation_case in automation_cases:
                user_case_id = automation_case.get('user_test_case_id')
                if user_case_id and user_case_id in user_case_map:
                    user_case = user_case_map[user_case_id]
                    automation_case['user_test_case_name'] = user_case.get('name', '未知用例')
                    automation_case['user_test_case'] = user_case
                else:
                    automation_case['user_test_case_name'] = '无关联用例'

        except Exception as e:
            print(f"获取用户测试用例信息失败: {e}")
            # 如果获取用户测试用例信息失败，设置默认值
            for automation_case in automation_cases:
                automation_case['user_test_case_name'] = '无关联用例'

    def batch_delete_automation_test_cases(self, case_ids: List[str], project_id: str) -> Dict:
        """批量删除自动化测试用例"""
        results = {
            'success_count': 0,
            'failed_count': 0,
            'errors': []
        }

        for case_id in case_ids:
            try:
                success = self.delete_automation_test_case(case_id, project_id)
                if success:
                    results['success_count'] += 1
                else:
                    results['failed_count'] += 1
                    results['errors'].append(f"自动化测试用例 {case_id} 删除失败")
            except Exception as e:
                results['failed_count'] += 1
                results['errors'].append(f"自动化测试用例 {case_id} 删除失败: {str(e)}")

        return results

    def batch_execute_automation_test_cases(self, case_ids: List[str], project_id: str) -> Dict:
        """批量执行自动化测试用例"""
        results = {
            'success_count': 0,
            'failed_count': 0,
            'errors': []
        }

        for case_id in case_ids:
            try:
                # 更新测试用例状态为执行中
                update_data = {
                    'status': '执行中',
                    'last_executed': datetime.now().isoformat()
                }
                success = self.update_automation_test_case(case_id, update_data, project_id)

                if success:
                    results['success_count'] += 1
                    # 这里可以添加实际的测试执行逻辑
                    # 目前只是更新状态，实际执行需要集成测试框架
                else:
                    results['failed_count'] += 1
                    results['errors'].append(f"自动化测试用例 {case_id} 执行启动失败")
            except Exception as e:
                results['failed_count'] += 1
                results['errors'].append(f"自动化测试用例 {case_id} 执行启动失败: {str(e)}")

        return results
