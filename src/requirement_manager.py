#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from datetime import datetime
from typing import Dict, List, Optional
from .storage import StorageManager
from .utils.llm_client import LLMClient
from .config import Config

class RequirementManager:
    """需求管理器"""
    
    def __init__(self, storage: StorageManager, config:Config):
        self.storage = storage
        self.config = config
        self.llm_client = LLMClient(config)
        self.prompts_path = config.PROMPT_PATH
        
        # 确保提示词目录存在
        os.makedirs(self.prompts_path, exist_ok=True)        
    
    def extract_requirements_from_document(self, document_id: str, project_id: str,
                                         batch_by_sections: bool = True, section_separator: str = '',
                                         max_threads: int = 4,
                                         progress_callback=None) -> Dict:
        """从文档中提取需求功能点"""
        # 获取文档内容
        from .document_manager import DocumentManager
        doc_manager = DocumentManager(self.storage, self.config)
        if not section_separator:
            section_separator = self.config.MARKDOWN_SEPARATOR
        
        document = doc_manager.get_document(document_id, project_id)
        if not document:
            raise ValueError("文档不存在")
        
        # 读取提示词
        system_prompt = self.load_requirement_prompt()
        
        results = {
            'total_extracted': 0,
            'sections_processed': 0,
            'requirements': [],
            'errors': []
        }
        
        try:
            if batch_by_sections:
                # 按章节批次处理
                sections = doc_manager.get_document_sections(document_id, project_id, section_separator)

                # 多线程并发处理
                results = self._extract_requirements_multithreaded(
                    sections, system_prompt, document_id, project_id, max_threads, progress_callback
                )                
            else:
                # 整个文档一次性处理
                content = doc_manager.get_document_content(document_id, project_id)
                if content:
                    print(f"开始调用LLM提取文档--[ {document_id} ]--的功能点。")
                    document_requirements = self.llm_client.extract_requirements(content, system_prompt)
                    print(f"  已提取功能点个数：{len(document_requirements)}.")
                    
                    for req in document_requirements:
                        req['document_id'] = document_id
                        req['project_id'] = project_id

                        # 确保title字段存在
                        if not req.get('title'):
                            req['title'] = req.get('description', '未命名需求')[:50]

                        # 生成编号
                        if not req.get('number'):
                            req['number'] = f"REQ-{len(results['requirements']) + 1:03d}"
                    
                    results['requirements'] = document_requirements
                    results['sections_processed'] = 1
            
            results['total_extracted'] = len(results['requirements'])

            # 自动保存提取的需求
            if results['requirements']:
                try:
                    saved_ids = self.save_extracted_requirements(results['requirements'])
                    results['saved_ids'] = saved_ids
                    results['saved_count'] = len(saved_ids)
                    print(f"自动保存了 {len(saved_ids)} 个需求功能点")
                except Exception as e:
                    print(f"自动保存需求失败: {e}")
                    results['errors'].append(f"保存需求失败: {str(e)}")

        except Exception as e:
            results['errors'].append(f"需求提取失败: {str(e)}")

        return results
    
    def save_extracted_requirements(self, requirements: List[Dict]) -> List[str]:
        """保存提取的需求功能点"""
        saved_ids = []
        
        for req in requirements:
            try:
                # 设置默认状态和AI生成标记
                req['status'] = req.get('status', '待处理')
                req['created_at'] = datetime.now().isoformat()
                req['updated_at'] = datetime.now().isoformat()
                req['ai_generated'] = True
                req['ai_modified_at'] = None

                # 保存需求
                req_id = self.storage.create('requirements', req, req['project_id'])
                saved_ids.append(req_id)

            except Exception as e:
                print(f"保存需求失败: {e}")
        
        return saved_ids
    
    def create_requirement(self, project_id: str, data: Dict) -> str:
        """创建需求功能点"""
        # 生成编号
        if not data.get('number'):
            existing_reqs = self.storage.list('requirements', project_id=project_id, page_size=1000)
            count = existing_reqs['total'] + 1
            data['number'] = f"REQ-{count:03d}"

        data['project_id'] = project_id
        data['status'] = data.get('status', '待处理')
        data['created_at'] = datetime.now().isoformat()
        data['updated_at'] = datetime.now().isoformat()
        data['ai_generated'] = data.get('ai_generated', False)

        return self.storage.create('requirements', data, project_id)
    
    def get_requirement(self, requirement_id: str, project_id: str = None) -> Optional[Dict]:
        """获取需求详情"""
        return self.storage.read('requirements', requirement_id, project_id)
    
    def update_requirement(self, requirement_id: str, data: Dict, project_id: str = None) -> bool:
        """更新需求功能点"""
        data['updated_at'] = datetime.now().isoformat()
        # 如果是AI生成的需求被修改，记录修改时间
        existing_req = self.storage.read('requirements', requirement_id, project_id)
        if existing_req and existing_req.get('ai_generated') and not existing_req.get('ai_modified_at'):
            data['ai_modified_at'] = datetime.now().isoformat()

        return self.storage.update('requirements', requirement_id, data, project_id)
    
    def delete_requirement(self, requirement_id: str, project_id: str = None) -> bool:
        """删除需求功能点"""
        return self.storage.delete('requirements', requirement_id, project_id)
    
    def list_requirements(self, project_id: str, page: int = 1, page_size: int = 20,
                         filters: Dict = None, search: str = None) -> Dict:
        """获取需求列表"""
        if search:
            # 搜索需求
            requirements = self.storage.search('requirements', search, project_id,
                                             fields=['number', 'description', 'section'])
            
            # 应用过滤器
            if filters:
                filtered_requirements = []
                for req in requirements:
                    match = True
                    for key, value in filters.items():
                        if value and req.get(key) != value:
                            match = False
                            break
                    if match:
                        filtered_requirements.append(req)
                requirements = filtered_requirements
            
            total = len(requirements)
            start = (page - 1) * page_size
            end = start + page_size
            page_requirements = requirements[start:end]
            
            return {
                'records': page_requirements,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
        else:
            return self.storage.list('requirements', project_id=project_id,
                                   filters=filters, page=page, page_size=page_size)
    
    def batch_delete_requirements(self, requirement_ids: List[str], project_id: str) -> Dict:
        """批量删除需求功能点"""
        results = {
            'success_count': 0,
            'failed_count': 0,
            'errors': []
        }
        
        for req_id in requirement_ids:
            try:
                success = self.delete_requirement(req_id, project_id)
                if success:
                    results['success_count'] += 1
                else:
                    results['failed_count'] += 1
                    results['errors'].append(f"需求 {req_id} 删除失败")
            except Exception as e:
                results['failed_count'] += 1
                results['errors'].append(f"需求 {req_id} 删除失败: {str(e)}")
        
        return results
    
    def get_requirement_types(self) -> List[str]:
        """获取需求类型列表"""
        return ['功能需求', '性能需求', '安全需求', '接口需求', '数据需求', '业务需求', '用户需求']
    
    def get_requirement_priorities(self) -> List[str]:
        """获取需求优先级列表"""
        return ['高', '中', '低']
    
    def get_requirement_statuses(self) -> List[str]:
        """获取需求状态列表"""
        return ['待处理', '处理中', '已完成']
    
    def load_requirement_prompt(self) -> str:
        """加载需求提取提示词"""
        prompt_file = os.path.join(self.prompts_path, self.config.PROMPT_REQUIREMENTS_FILE)
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception:
            raise Exception("无法加载需求提取提示词")
    
    def update_requirement_prompt(self, prompt: str) -> bool:
        """更新需求提取提示词"""
        prompt_file = os.path.join(self.prompts_path, self.config.PROMPT_REQUIREMENTS_FILE)
        try:
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(prompt)
            return True
        except Exception as e:
            print(f"更新提示词失败: {e}")
            return False
    
    def get_requirements_by_document(self, document_id: str, project_id: str) -> List[Dict]:
        """获取指定文档的需求功能点"""
        filters = {'document_id': document_id}
        result = self.storage.list('requirements', project_id=project_id, 
                                 filters=filters, page_size=1000)
        return result['records']
    
    def get_requirement_statistics(self, project_id: str) -> Dict:
        """获取需求统计信息"""
        all_requirements = self.storage.list('requirements', project_id=project_id, 
                                           page_size=1000)['records']
        
        stats = {
            'total': len(all_requirements),
            'by_type': {},
            'by_priority': {},
            'by_status': {}
        }
        
        for req in all_requirements:
            # 按类型统计
            req_type = req.get('type', '未分类')
            stats['by_type'][req_type] = stats['by_type'].get(req_type, 0) + 1
            
            # 按优先级统计
            priority = req.get('priority', '未设置')
            stats['by_priority'][priority] = stats['by_priority'].get(priority, 0) + 1
            
            # 按状态统计
            status = req.get('status', '未设置')
            stats['by_status'][status] = stats['by_status'].get(status, 0) + 1
        
        return stats

    def _extract_requirements_multithreaded(self, sections: List[Dict], system_prompt: str,
                                           document_id: str, project_id: str, max_threads: int,
                                           progress_callback=None) -> Dict:
        """多线程提取需求功能点"""
        import threading
        from concurrent.futures import ThreadPoolExecutor, as_completed
        import time

        results = {
            'total_extracted': 0,
            'sections_processed': 0,
            'requirements': [],
            'errors': []
        }

        # 线程安全的锁
        results_lock = threading.Lock()

        completed_count = [0]
        def process_section(section):
            """处理单个章节"""
            try:
                print(f'[线程] 开始调用LLM提取章节--[ {section.get("title","无标题")} ]--的功能点。')
                section_requirements = self.llm_client.extract_requirements(
                    section['content'], system_prompt, "/nothink"
                )
                print(f"[线程]   已提取功能点个数：{len(section_requirements)}.")
                # 更新进度
                if progress_callback:
                    with results_lock:
                        progress = int(100 * (completed_count[0] + 1) / len(sections))
                        progress_callback(progress)
                        completed_count[0] += 1

                # 为每个需求添加章节信息
                for req in section_requirements:
                    req['section'] = section['title']
                    req['document_id'] = document_id
                    req['project_id'] = project_id

                    # 确保title字段存在
                    if not req.get('title'):
                        req['title'] = req.get('description', '未命名需求')[:50]

                return {
                    'success': True,
                    'requirements': section_requirements,
                    'section_title': section['title']
                }

            except Exception as e:
                return {
                    'success': False,
                    'error': f"章节 '{section['title']}' 处理失败: {str(e)}",
                    'section_title': section['title']
                }

        # 使用线程池执行
        print(f"使用 {max_threads} 个线程并发处理 {len(sections)} 个章节")

        with ThreadPoolExecutor(max_workers=max_threads) as executor:
            # 提交所有任务
            future_to_section = {executor.submit(process_section, section): section
                               for section in sections}

            # 收集结果
            for future in as_completed(future_to_section):
                section = future_to_section[future]
                try:
                    result = future.result()

                    with results_lock:
                        if result['success']:
                            # 为需求生成编号
                            for req in result['requirements']:
                                if not req.get('number'):
                                    req['number'] = f"REQ-{len(results['requirements']) + 1:03d}"

                            results['requirements'].extend(result['requirements'])
                            results['sections_processed'] += 1
                        else:
                            results['errors'].append(result['error'])

                except Exception as e:
                    with results_lock:
                        results['errors'].append(f"章节 '{section['title']}' 处理异常: {str(e)}")

        results['total_extracted'] = len(results['requirements'])
        print(f"多线程处理完成，共提取 {results['total_extracted']} 个需求功能点")

        return results
