#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
REST API服务：根据需求生成测试用例
提供独立的API端点供外部系统调用
"""

from flask import Flask, request, jsonify
from typing import List, Dict, Any
import json
import uuid
from datetime import datetime

from .config import Config
from .storage import StorageManager
from .test_manager import TestManager
from .requirement_manager import RequirementManager
from .knowledge_manager import KnowledgeManager
from .utils.llm_client import LLMClient


class TestCaseGenerationAPI:
    """测试用例生成API服务"""
    
    def __init__(self):
        self.config = Config()
        self.storage = StorageManager(self.config)
        self.knowledge_manager = KnowledgeManager(self.storage, self.config)
        self.llm_client = LLMClient(self.config)
        self.test_manager = TestManager(self.storage, self.config)
        self.requirement_manager = RequirementManager(self.storage, self.config)
    
    def create_app(self):
        """创建Flask应用"""
        app = Flask(__name__)
        
        @app.route('/aitest/v1/generate_test_cases', methods=['POST'])
        def generate_test_cases():
            """根据需求生成测试用例的API端点"""
            try:
                # 获取请求数据
                data = request.get_json()
                if not data:
                    return jsonify({
                        'success': False,
                        'message': '请求数据不能为空'
                    }), 400
                
                # 验证必填字段
                project_id = data.get('project_id')
                if not project_id:
                    return jsonify({
                        'success': False,
                        'message': 'project_id字段不能为空'
                    }), 400
                
                requirements_data = data.get('requirements', [])
                if not requirements_data:
                    return jsonify({
                        'success': False,
                        'message': 'requirements字段不能为空'
                    }), 400
                
                # 验证需求数据格式
                for req in requirements_data:
                    if not isinstance(req, dict):
                        return jsonify({
                            'success': False,
                            'message': '需求数据格式错误，应为对象数组'
                        }), 400
                
                # 转换需求数据格式，添加必要字段
                processed_requirements = []
                for req in requirements_data:
                    processed_req = {
                        'id': str(uuid.uuid4()),
                        'project_id': project_id,
                        'number': req.get('number', ''),
                        'title': req.get('title', ''),
                        'spec': req.get('spec', ''),
                        'businessProcess': req.get('businessProcess', ''),
                        'constraints': req.get('constraints', ''),
                        'verify': req.get('verify', ''),
                        'comment': req.get('comment', ''),
                        'description': req.get('spec', ''),  # 兼容旧字段
                        'priority': req.get('priority', '中'),
                        'type': req.get('type', '功能需求'),
                        'status': '待处理',
                        'created_at': datetime.now().isoformat(),
                        'updated_at': datetime.now().isoformat(),
                        'ai_generated': False
                    }
                    processed_requirements.append(processed_req)
                
                # 获取测试类型，默认为功能测试
                test_types = data.get('test_types', ['functional'])
                use_knowledge_base = data.get('use_knowledge_base', True)
                max_threads = data.get('max_threads', 4)
                
                # 调用测试管理器生成测试用例
                result = self.test_manager.generate_test_cases_from_requirements(
                    project_id=project_id,
                    requirements=processed_requirements,
                    test_types=test_types,
                    use_knowledge_base=use_knowledge_base,
                    max_threads=max_threads
                )
                
                if result['success']:
                    # 转换输出格式，符合api_resp.json格式
                    formatted_test_cases = []
                    for test_case in result['test_cases']:
                        formatted_case = {
                            'name': test_case.get('name', ''),
                            'precondition': test_case.get('precondition', ''),
                            'stepsJson': test_case.get('stepsJson', ''),
                            'expectsJson': test_case.get('expectsJson', ''),
                            'keywords': test_case.get('keywords', ''),
                            'pri': test_case.get('pri', '2'),
                            'type': test_case.get('type', 'functional'),
                            'auto': test_case.get('auto', 'no'),
                            'distinguish': test_case.get('distinguish', '0'),
                            'executionHours': test_case.get('executionHours', '1'),
                            'stage': test_case.get('stage', '功能测试阶段'),
                            'req_number': test_case.get('req_number', ''),
                            'project_id': project_id,
                            'requirement_ids': test_case.get('requirement_ids', [])
                        }
                        formatted_test_cases.append(formatted_case)
                    
                    return jsonify({
                        'success': True,
                        'data': {
                            'test_cases': formatted_test_cases,
                            'total_generated': len(formatted_test_cases),
                            'saved_ids': result.get('saved_ids', [])
                        },
                        'message': f'成功生成 {len(formatted_test_cases)} 个测试用例'
                    })
                else:
                    return jsonify({
                        'success': False,
                        'message': result.get('error', '生成测试用例失败'),
                        'errors': result.get('errors', [])
                    }), 500
                    
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'服务器内部错误: {str(e)}'
                }), 500
        
        @app.route('/aitest/v1/health', methods=['GET'])
        def health_check():
            """健康检查端点"""
            return jsonify({
                'status': 'healthy',
                'service': 'test-case-generation-api',
                'timestamp': datetime.now().isoformat()
            })
        
        return app


def create_api_app():
    """创建API应用的工厂函数"""
    api_service = TestCaseGenerationAPI()
    return api_service.create_app()


if __name__ == '__main__':
    # 独立运行API服务
    app = create_api_app()
    app.run(host='0.0.0.0', port=5001, debug=True)
