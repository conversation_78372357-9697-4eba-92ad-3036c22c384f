{% extends "base.html" %}

{% block title %}需求管理 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/aitest">首页</a></li>
<li class="breadcrumb-item active">需求管理</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-list me-2"></i>需求功能点管理
            </h1>
            <div>
                <button class="btn btn-success me-2" onclick="showExtractModal()" id="extractBtn" disabled>
                    <i class="fas fa-magic me-1"></i>提取需求
                </button>
                <button class="btn btn-primary" onclick="showCreateModal()" id="createBtn" disabled>
                    <i class="fas fa-plus me-1"></i>添加需求
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 项目选择提示 -->
<div class="row" id="projectPrompt">
    <div class="col-12">
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            请先在顶部导航栏选择一个项目
        </div>
    </div>
</div>

<!-- 需求列表 -->
<div class="row" id="requirementsSection" style="display: none;">
    <div class="col-12">
        <!-- 搜索和过滤 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="search-box">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索需求编号或描述...">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="typeFilter">
                    <option value="">所有类型</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="priorityFilter">
                    <option value="">所有优先级</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                    <option value="">所有状态</option>
                </select>
            </div>
            <div class="col-md-1">
                <select class="form-select" id="pageSizeSelect" onchange="changePageSize()">
                    <option value="10" selected>10条</option>
                    <option value="30">30条</option>
                    <option value="100">100条</option>
                </select>
            </div>
            <div class="col-md-2 text-end">
                <button class="btn btn-outline-secondary" onclick="refreshRequirements()">
                    <i class="fas fa-sync me-1"></i>刷新
                </button>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <div class="col-md-6">
                        <button class="btn btn-danger btn-sm" onclick="batchDelete()" id="batchDeleteBtn" style="display: none;">
                            <i class="fas fa-trash me-1"></i>批量删除
                        </button>
                        <button class="btn btn-success btn-sm ms-2" onclick="generateTestCases()" id="generateTestBtn" style="display: none;">
                            <i class="fas fa-vial me-1"></i>生成测试用例
                        </button>
                    </div>
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th>编号</th>
                                <th>标题</th>
                                <th>类型</th>
                                <th>优先级</th>
                                <th>状态</th>
                                <th>章节</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="requirementsTableBody">
                            <!-- 需求列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 批量操作 -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <!-- 分页 -->
                        <div id="pagination"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 提取需求模态框 -->
<div class="modal fade" id="extractModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">从文档提取需求</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="documentSelect" class="form-label">选择文档 <span class="text-danger">*</span></label>
                    <select class="form-select" id="documentSelect" required>
                        <option value="">请选择文档</option>
                    </select>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="batchBySection" checked onchange="toggleSectionOptions()">
                        <label class="form-check-label" for="batchBySection">
                            按章节批次处理
                        </label>
                        <div class="form-text">推荐开启，可避免超出大模型上下文长度限制</div>
                    </div>
                </div>
                <div class="mb-3" id="sectionOptions">
                    <label for="sectionSeparator" class="form-label">章节分隔符</label>
                    <select class="form-select" id="sectionSeparator">
                        <option value="###">### (三级标题)</option>
                        <option value="##">## (二级标题)</option>
                        <option value="#"># (一级标题)</option>
                        <option value="####">#### (四级标题)</option>
                        <option value="---">--- (分隔线)</option>
                        <option value="custom">自定义</option>
                    </select>
                    <div class="mt-2" id="customSeparatorDiv" style="display: none;">
                        <input type="text" class="form-control" id="customSeparator" placeholder="输入自定义分隔符">
                    </div>
                    <div class="form-text">选择用于识别文档章节的分隔符</div>
                </div>
                <div class="mb-3">
                    <label for="maxThreads" class="form-label">并发线程数</label>
                    <input type="number" class="form-control" id="maxThreads" min="1" max="10" value="4">
                    <div class="form-text">用于并发处理多个章节，提高提取速度</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="extractRequirements()">开始提取</button>
            </div>
        </div>
    </div>
</div>

<!-- 创建/编辑需求模态框 -->
<div class="modal fade" id="requirementModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="requirementModalTitle">添加需求</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="requirementForm">
                    <input type="hidden" id="requirementId">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="requirementNumber" class="form-label">需求编号</label>
                                <input type="text" class="form-control" id="requirementNumber" placeholder="自动生成">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="requirementSection" class="form-label">所属章节</label>
                                <input type="text" class="form-control" id="requirementSection">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="requirementType" class="form-label">需求类型</label>
                                <select class="form-select" id="requirementType">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="requirementPriority" class="form-label">优先级</label>
                                <select class="form-select" id="requirementPriority">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="requirementStatus" class="form-label">状态</label>
                                <select class="form-select" id="requirementStatus">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="requirementTitle" class="form-label">需求标题 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="requirementTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="requirementDescription" class="form-label">需求描述</label>
                        <textarea class="form-control" id="requirementDescription" rows="4"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRequirement()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 单个功能点生成测试用例模态框 -->
<div class="modal fade" id="singleTestGenerationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">生成测试用例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="singleRequirementId">
                <div class="mb-3">
                    <label class="form-label">功能点</label>
                    <p class="form-control-plaintext" id="singleRequirementTitle"></p>
                </div>

                <div class="mb-3">
                    <label class="form-label">选择测试类型</label>
                    <div id="singleTestTypes">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="ui" id="singleTestTypeUI" checked>
                            <label class="form-check-label" for="singleTestTypeUI">
                                <span class="badge bg-primary me-2">功能测试</span>
                                通过界面验证功能的操作步骤、预期结果等
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="api" id="singleTestTypeAPI">
                            <label class="form-check-label" for="singleTestTypeAPI">
                                <span class="badge bg-success me-2">接口测试</span>
                                通过API验证功能的操作步骤、预期结果等
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="system" id="singleTestTypeSystem">
                            <label class="form-check-label" for="singleTestTypeSystem">
                                <span class="badge bg-warning me-2">系统测试</span>
                                执行系统命令，验证功能点是否正常
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="performance" id="singleTestTypePerformance">
                            <label class="form-check-label" for="singleTestTypePerformance">
                                <span class="badge bg-danger me-2">性能测试</span>
                                根据功能点生成性能测试用例
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="security" id="singleTestTypeSecurity">
                            <label class="form-check-label" for="singleTestTypeSecurity">
                                <span class="badge bg-dark me-2">安全测试</span>
                                根据功能点生成安全测试用例
                            </label>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="singleUseKnowledgeBase" checked>
                        <label class="form-check-label" for="singleUseKnowledgeBase">
                            使用知识库增强生成效果
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="executeBatchTestGeneration()">生成测试用例</button>
            </div>
        </div>
    </div>
</div>

<!-- 查看测试用例模态框 -->
<div class="modal fade" id="testCasesModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testCasesModalTitle">测试用例列表</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="testCasesRequirementId">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>测试用例名称</th>
                                <th>测试类型</th>
                                <th>优先级</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="testCasesTableBody">
                            <!-- 测试用例列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量生成测试用例模态框 -->
<div class="modal fade" id="batchTestGenerationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量生成测试用例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="batchRequirementIds">
                <div class="mb-3">
                    <label class="form-label">选中的功能点</label>
                    <p class="form-control-plaintext">已选择 <span id="batchRequirementCount">0</span> 个功能点</p>
                </div>

                <div class="mb-3">
                    <label class="form-label">选择测试类型</label>
                    <div id="batchTestTypes">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="functional" id="batchTestTypeUI" checked>
                            <label class="form-check-label" for="batchTestTypeUI">
                                <span class="badge bg-primary me-2">功能测试</span>
                                通过界面验证功能的操作步骤、预期结果等
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="api" id="batchTestTypeAPI">
                            <label class="form-check-label" for="batchTestTypeAPI">
                                <span class="badge bg-success me-2">接口测试</span>
                                通过API验证功能的操作步骤、预期结果等
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="system" id="batchTestTypeSystem">
                            <label class="form-check-label" for="batchTestTypeSystem">
                                <span class="badge bg-warning me-2">系统测试</span>
                                执行系统命令，验证功能点是否正常
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="performance" id="batchTestTypePerformance">
                            <label class="form-check-label" for="batchTestTypePerformance">
                                <span class="badge bg-danger me-2">性能测试</span>
                                根据功能点生成性能测试用例
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="security" id="batchTestTypeSecurity">
                            <label class="form-check-label" for="batchTestTypeSecurity">
                                <span class="badge bg-dark me-2">安全测试</span>
                                根据功能点生成安全测试用例
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="functional" id="batchTestTypeFunctional" checked>
                            <label class="form-check-label" for="batchTestTypeFunctional">
                                <span class="badge bg-info me-2">功能测试</span>
                                基础功能测试用例
                            </label>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="batchUseKnowledgeBase" checked>
                        <label class="form-check-label" for="batchUseKnowledgeBase">
                            使用知识库增强生成效果
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="executeBatchTestGeneration()">生成测试用例</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentSearch = '';
let currentFilters = {};
let selectedRequirements = [];

$(document).ready(function() {
    checkCurrentProject();
    loadFilterOptions();
    
    // 搜索输入框事件
    $('#searchInput').on('input', debounce(function() {
        currentSearch = $(this).val();
        currentPage = 1;
        loadRequirements();
    }, 500));

    // 搜索输入框回车键事件
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) { // 回车键
            currentSearch = $(this).val();
            currentPage = 1;
            loadRequirements();
        }
    });
    
    // 过滤器事件
    $('#typeFilter, #priorityFilter, #statusFilter').change(function() {
        updateFilters();
        currentPage = 1;
        loadRequirements();
    });

    // 章节分隔符选择事件
    $('#sectionSeparator').change(handleSeparatorChange);

    // 初始化章节选项显示状态
    toggleSectionOptions();

    // 检查URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const extractFrom = urlParams.get('extract_from');
    if (extractFrom) {
        setTimeout(() => {
            $('#documentSelect').val(extractFrom);
            showExtractModal();
        }, 1000);
    }
});

// 检查当前项目
function checkCurrentProject() {
    $.get('/aitest/api/current_project', function(response) {
        if (response.project_id) {
            $('#projectPrompt').hide();
            $('#requirementsSection').show();
            $('#extractBtn, #createBtn').prop('disabled', false);
            loadRequirements();
            loadDocuments();
        } else {
            $('#projectPrompt').show();
            $('#requirementsSection').hide();
            $('#extractBtn, #createBtn').prop('disabled', true);
        }
    });
}

// 加载过滤器选项
function loadFilterOptions() {
    // 加载需求类型
    $.get('/aitest/requirements/api/types', function(response) {
        if (response.success) {
            const typeSelect = $('#requirementType, #typeFilter');
            response.data.forEach(type => {
                typeSelect.append(`<option value="${type}">${type}</option>`);
            });
        }
    });
    
    // 加载优先级
    $.get('/aitest/requirements/api/priorities', function(response) {
        if (response.success) {
            const prioritySelect = $('#requirementPriority, #priorityFilter');
            response.data.forEach(priority => {
                prioritySelect.append(`<option value="${priority}">${priority}</option>`);
            });
        }
    });
    
    // 加载状态
    $.get('/aitest/requirements/api/statuses', function(response) {
        if (response.success) {
            const statusSelect = $('#requirementStatus, #statusFilter');
            response.data.forEach(status => {
                statusSelect.append(`<option value="${status}">${status}</option>`);
            });
        }
    });
}

// 加载文档列表
function loadDocuments() {
    $.get('/aitest/documents/api/list?page_size=100', function(response) {
        if (response.success) {
            const select = $('#documentSelect');
            select.find('option:not(:first)').remove();
            response.data.records.forEach(doc => {
                select.append(`<option value="${doc.id}">${doc.name}</option>`);
            });
        }
    });
}

// 更新过滤器
function updateFilters() {
    currentFilters = {};
    const type = $('#typeFilter').val();
    const priority = $('#priorityFilter').val();
    const status = $('#statusFilter').val();
    
    if (type) currentFilters.type = type;
    if (priority) currentFilters.priority = priority;
    if (status) currentFilters.status = status;
}

// 加载需求列表
function loadRequirements() {
    const pageSize = parseInt($('#pageSizeSelect').val()) || 10;
    const params = {
        page: currentPage,
        page_size: pageSize,
        ...currentFilters
    };
    
    if (currentSearch) {
        params.search = currentSearch;
    }
    
    $.get('/aitest/requirements/api/list', params, function(response) {
        if (response.success) {
            // 按编号排序
            let requirements = response.data.records || [];
            requirements.sort(function(a, b) {
                const numA = a.number || '';
                const numB = b.number || '';

                // 提取数字部分进行比较
                const extractNumber = (str) => {
                    const match = str.match(/\d+/);
                    return match ? parseInt(match[0]) : 0;
                };

                const numberA = extractNumber(numA);
                const numberB = extractNumber(numB);

                if (numberA !== numberB) {
                    return numberA - numberB;
                }

                // 如果数字相同，按字符串比较
                return numA.localeCompare(numB);
            });

            renderRequirementsTable(requirements);
            initPagination('pagination', response.data.total_pages, currentPage, function(page) {
                currentPage = page;
                loadRequirements();
            });
        } else {
            showNotification('加载需求列表失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载需求列表失败', 'error');
    });
}

// 渲染需求表格
function renderRequirementsTable(requirements) {
    const tbody = $('#requirementsTableBody');
    tbody.empty();
    
    if (requirements.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-list fa-2x mb-2"></i><br>
                    暂无需求数据
                </td>
            </tr>
        `);
        return;
    }
    
    requirements.forEach(function(req) {
        const row = $(`
            <tr>
                <td>
                    <input type="checkbox" class="requirement-checkbox" value="${req.id}" onchange="updateSelection()">
                </td>
                <td>
                    <a href="javascript:void(0)" onclick="editRequirement('${req.id}')" class="text-decoration-none">
                        <strong>${req.number || '-'}</strong>
                    </a>
                </td>
                <td>
                    <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis;">
                        <a href="javascript:void(0)" onclick="editRequirement('${req.id}')" class="text-decoration-none"
                           title="${req.description || req.title || ''}" data-bs-toggle="tooltip" data-bs-placement="top">
                            <strong>${req.title || req.description || '-'}</strong>
                        </a>
                        ${req.description && req.title ? '<br><small class="text-muted">' + req.description.substring(0, 20) + (req.description.length > 20 ? '...' : '') + '</small>' : ''}
                    </div>
                </td>
                <td>${getTypeBadge(req.type || '-')}</td>
                <td>${getPriorityBadge(req.priority || '-')}</td>
                <td>${getStatusBadge(req.status || '-')}</td>
                <td><small class="text-muted">${req.section || '-'}</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="generateTestCasesForRequirement('${req.id}', '${req.title || req.description || ''}')" title="生成测试用例">
                            <i class="fas fa-vial"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewTestCasesForRequirement('${req.id}', '${req.title || req.description || ''}')" title="查看测试用例">
                            <i class="fas fa-list"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editRequirement('${req.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteRequirement('${req.id}', '${req.number}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
        tbody.append(row);
    });

    // 初始化工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();
}

// 显示提取模态框
function showExtractModal() {
    const modal = new bootstrap.Modal($('#extractModal')[0]);
    modal.show();
}

// 切换章节选项显示
function toggleSectionOptions() {
    const batchBySection = $('#batchBySection').is(':checked');
    const sectionOptions = $('#sectionOptions');

    if (batchBySection) {
        sectionOptions.show();
    } else {
        sectionOptions.hide();
    }
}

// 处理分隔符选择变化
function handleSeparatorChange() {
    const separatorSelect = $('#sectionSeparator').val();
    const customDiv = $('#customSeparatorDiv');

    if (separatorSelect === 'custom') {
        customDiv.show();
    } else {
        customDiv.hide();
    }
}

// 显示创建模态框
function showCreateModal() {
    $('#requirementModalTitle').text('添加需求');
    $('#requirementForm')[0].reset();
    $('#requirementId').val('');
    const modal = new bootstrap.Modal($('#requirementModal')[0]);
    modal.show();
}

// 编辑需求
function editRequirement(requirementId) {
    $.get(`/aitest/requirements/api/${requirementId}`, function(response) {
        if (response.success) {
            const req = response.data;
            $('#requirementModalTitle').text('编辑需求');
            $('#requirementId').val(req.id);
            $('#requirementNumber').val(req.number || '');
            $('#requirementSection').val(req.section || '');
            $('#requirementType').val(req.type || '');
            $('#requirementPriority').val(req.priority || '');
            $('#requirementStatus').val(req.status || '');
            $('#requirementTitle').val(req.title || '');
            $('#requirementDescription').val(req.description || '');

            const modal = new bootstrap.Modal($('#requirementModal')[0]);
            modal.show();
        } else {
            showNotification('获取需求信息失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('获取需求信息失败', 'error');
    });
}

// 保存需求
function saveRequirement() {
    const requirementId = $('#requirementId').val();
    const data = {
        number: $('#requirementNumber').val().trim(),
        section: $('#requirementSection').val().trim(),
        type: $('#requirementType').val(),
        priority: $('#requirementPriority').val(),
        status: $('#requirementStatus').val(),
        title: $('#requirementTitle').val().trim(),
        description: $('#requirementDescription').val().trim()
    };

    if (!data.title) {
        showNotification('请输入需求标题', 'error');
        return;
    }

    const url = requirementId ? `/aitest/requirements/api/${requirementId}/update` : '/aitest/requirements/api/create';
    const method = requirementId ? 'PUT' : 'POST';

    showProgress('正在保存需求...');

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            hideProgress();
            if (response.success) {
                showNotification(response.message, 'success');
                bootstrap.Modal.getInstance($('#requirementModal')[0]).hide();
                loadRequirements();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            hideProgress();
            let errorMessage = '保存需求失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            showNotification(errorMessage, 'error');
        },
        complete: function() {
            setTimeout(hideProgress, 100);
        }
    });
}

// 提取需求
function extractRequirements() {
    const documentId = $('#documentSelect').val();
    const batchBySection = $('#batchBySection').is(':checked');

    if (!documentId) {
        showNotification('请选择文档', 'error');
        return;
    }

    // 获取章节分隔符
    let sectionSeparator = '###'; // 默认值
    if (batchBySection) {
        const separatorSelect = $('#sectionSeparator').val();
        if (separatorSelect === 'custom') {
            sectionSeparator = $('#customSeparator').val().trim();
            if (!sectionSeparator) {
                showNotification('请输入自定义分隔符', 'error');
                return;
            }
        } else {
            sectionSeparator = separatorSelect;
        }
    }

    const maxThreads = parseInt($('#maxThreads').val()) || 4;

    showProgress('正在提取需求功能点...');

    $.ajax({
        url: '/aitest/requirements/api/extract',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            document_id: documentId,
            batch_by_sections: batchBySection,
            section_separator: sectionSeparator,
            max_threads: maxThreads
        }),
        success: function(response) {
            hideProgress();
            if (response.success) {
                showNotification(response.message, 'success');
                bootstrap.Modal.getInstance($('#extractModal')[0]).hide();

                // 跳转到任务管理页面查看进度
                if (response.task_id) {
                    showTaskProgress(response.task_id, '需求提取');
                }
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            hideProgress();
            console.error('提取需求错误:', xhr, status, error);

            let errorMessage = '提取需求失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status >= 500) {
                errorMessage = '服务器错误，请稍后重试';
            }

            showNotification(errorMessage, 'error');
        },
        complete: function() {
            setTimeout(hideProgress, 100);
        }
    });
}

// 保存提取的需求
function saveExtractedRequirements(requirements) {
    showProgress('正在保存需求...');
    
    $.ajax({
        url: '/aitest/requirements/api/save_extracted',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({requirements: requirements}),
        success: function(response) {
            hideProgress();
            if (response.success) {
                showNotification(response.message, 'success');
                loadRequirements();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            hideProgress();
            showNotification('保存需求失败', 'error');
        },
        complete: function() {
            setTimeout(hideProgress, 100);
        }
    });
}



// 更新选择状态
function updateSelection() {
    selectedRequirements = [];
    $('.requirement-checkbox:checked').each(function() {
        selectedRequirements.push($(this).val());
    });
    
    if (selectedRequirements.length > 0) {
        $('#batchDeleteBtn, #generateTestBtn').show();
    } else {
        $('#batchDeleteBtn, #generateTestBtn').hide();
    }
}


// 刷新页面数据
function refreshPageData() {
    checkCurrentProject();
}

// 改变分页大小
function changePageSize() {
    currentPage = 1; // 重置到第一页
    loadRequirements();
}

// 更新选择状态
function updateSelection() {
    const selectedRequirements = $('.requirement-checkbox:checked');
    const batchDeleteBtn = $('#batchDeleteBtn');
    const generateTestBtn = $('#generateTestBtn');

    if (selectedRequirements.length > 0) {
        batchDeleteBtn.show();
        generateTestBtn.show();
    } else {
        batchDeleteBtn.hide();
        generateTestBtn.hide();
    }

    // 更新全选状态
    const totalCheckboxes = $('.requirement-checkbox').length;
    const checkedCheckboxes = selectedRequirements.length;
    const selectAllCheckbox = $('#selectAll');

    if (checkedCheckboxes === 0) {
        selectAllCheckbox.prop('indeterminate', false);
        selectAllCheckbox.prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        selectAllCheckbox.prop('indeterminate', false);
        selectAllCheckbox.prop('checked', true);
    } else {
        selectAllCheckbox.prop('indeterminate', true);
    }
}

// 切换全选
function toggleSelectAll() {
    const isChecked = $('#selectAll').is(':checked');
    $('.requirement-checkbox').prop('checked', isChecked);
    updateSelection();
}

// 批量删除
function batchDelete() {
    const selectedRequirements = $('.requirement-checkbox:checked');
    if (selectedRequirements.length === 0) {
        showNotification('请选择要删除的需求', 'warning');
        return;
    }

    const requirementIds = [];
    selectedRequirements.each(function() {
        requirementIds.push($(this).val());
    });

    if (confirm(`确定要删除选中的 ${requirementIds.length} 个需求吗？此操作不可恢复。`)) {
        showProgress('正在删除需求...');

        $.ajax({
            url: '/aitest/requirements/api/batch_delete',
            method: 'DELETE',
            contentType: 'application/json',
            data: JSON.stringify({requirement_ids: requirementIds}),
            success: function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    loadRequirements();
                    $('#selectAll').prop('checked', false);
                    updateSelection();
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = '批量删除失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showNotification(errorMessage, 'error');
            },
            complete: function() {
                hideProgress();
            }
        });
    }
}

// 生成测试用例
function generateTestCases() {
    const selectedRequirements = $('.requirement-checkbox:checked');
    if (selectedRequirements.length === 0) {
        showNotification('请选择要生成测试用例的需求', 'warning');
        return;
    }

    const requirementIds = [];
    selectedRequirements.each(function() {
        requirementIds.push($(this).val());
    });

    // 显示批量生成测试用例模态框
    $('#batchTestGenerationModal').modal('show');
    $('#batchRequirementIds').val(JSON.stringify(requirementIds));
    $('#batchRequirementCount').text(requirementIds.length);

    // 重置表单
    $('#batchTestTypes input[type="checkbox"]').prop('checked', false);
    $('#batchTestTypeUI').prop('checked', true);// UI测试默认选中
    $('#batchUseKnowledgeBase').prop('checked', true);
}

// 执行批量测试用例生成
function executeBatchTestGeneration() {
    // 判断是批量还是单个生成
    const isBatchMode = $('#batchTestGenerationModal').hasClass('show');
    const isSingleMode = $('#singleTestGenerationModal').hasClass('show');
    
    let requirementIds = [];
    let selectedTypes = [];
    let useKnowledgeBase = false;
    
    if (isBatchMode) {
        // 批量生成模式
        requirementIds = JSON.parse($('#batchRequirementIds').val());
        $('#batchTestTypes input[type="checkbox"]:checked').each(function() {
            selectedTypes.push($(this).val());
        });
        useKnowledgeBase = $('#batchUseKnowledgeBase').is(':checked');
    } else if (isSingleMode) {
        // 单个生成模式
        const requirementId = $('#singleRequirementId').val();
        requirementIds = [requirementId];
        $('#singleTestTypes input[type="checkbox"]:checked').each(function() {
            selectedTypes.push($(this).val());
        });
        useKnowledgeBase = $('#singleUseKnowledgeBase').is(':checked');
    } else {
        showNotification('未检测到有效的生成模式', 'error');
        return;
    }

    if (selectedTypes.length === 0) {
        showNotification('请至少选择一种测试类型', 'error');
        return;
    }

    // 关闭模态框
    if (isBatchMode) {
        $('#batchTestGenerationModal').modal('hide');
    } else if (isSingleMode) {
        $('#singleTestGenerationModal').modal('hide');
    }

    const data = {
        requirement_ids: requirementIds,
        test_types: selectedTypes,
        use_knowledge_base: useKnowledgeBase
    };

    showProgress('正在根据需求生成测试用例，请稍候...');

    $.ajax({
        url: '/aitest/tests/api/generate',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        timeout: 300000, // 5分钟超时
        success: function(response) {
            hideProgress();
            if (response && response.success) {
                const message = response.data?.message || response.message || '测试用例生成成功';
                showNotification(message, 'success');
                // 跳转到任务管理页面查看进度
                if (response.task_id) {
                    showTaskProgress(response.task_id, '生成用例');
                }
            } else {
                const errorMessage = response?.message || '生成测试用例失败';
                showNotification('生成测试用例失败: ' + errorMessage, 'error');
            }
        },
        error: function(xhr, status, error) {
            hideProgress();
            console.error('生成测试用例错误:', xhr, status, error);

            let errorMessage = '生成测试用例失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (status === 'timeout') {
                errorMessage = '生成超时，请稍后重试';
            } else if (xhr.status >= 500) {
                errorMessage = '服务器错误，请稍后重试';
            } else if (xhr.status >= 400) {
                errorMessage = '请求错误: ' + (xhr.responseJSON?.message || xhr.statusText);
            }

            showNotification(errorMessage, 'error');
        },
        complete: function() {
            // 确保进度框被隐藏
            setTimeout(hideProgress, 100);
        }
    });
}

// 删除单个需求
function deleteRequirement(requirementId, requirementNumber) {
    if (confirm(`确定要删除需求"${requirementNumber}"吗？`)) {
        $.ajax({
            url: `/aitest/requirements/api/${requirementId}/delete`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    loadRequirements();
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function() {
                showNotification('删除需求失败', 'error');
            }
        });
    }
}

// 显示任务进度
function showTaskProgress(taskId, taskName) {
    // 创建任务进度模态框
    const modalHtml = `
        <div class="modal fade" id="taskProgressModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${taskName}进度</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p id="taskStatus">任务正在执行中...</p>
                            <div class="progress mb-3">
                                <div class="progress-bar" id="taskProgressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <p class="text-muted">任务ID: ${taskId}</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="window.open('/aitest/tasks/', '_blank')">查看所有任务</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    $('#taskProgressModal').remove();

    // 添加新的模态框
    $('body').append(modalHtml);

    // 显示模态框
    $('#taskProgressModal').modal('show');

    // 定期检查任务状态
    const checkInterval = setInterval(() => {
        $.ajax({
            url: `/aitest/api/tasks/status/${taskId}`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    const task = response.data;
                    $('#taskProgressBar').css('width', task.progress + '%');

                    if (task.status === 'completed') {
                        $('#taskStatus').text('任务已完成！');
                        $('.spinner-border').hide();
                        clearInterval(checkInterval);

                        // 3秒后自动关闭并刷新列表
                        setTimeout(() => {
                            $('#taskProgressModal').modal('hide');
                            loadRequirements();
                        }, 3000);
                    } else if (task.status === 'failed') {
                        $('#taskStatus').text('任务执行失败: ' + (task.error || '未知错误'));
                        $('.spinner-border').hide();
                        clearInterval(checkInterval);
                    } else if (task.status === 'cancelled') {
                        $('#taskStatus').text('任务已取消');
                        $('.spinner-border').hide();
                        clearInterval(checkInterval);
                    } else {
                        $('#taskStatus').text('任务正在执行中...');
                    }
                }
            },
            error: function() {
                $('#taskStatus').text('无法获取任务状态');
                $('.spinner-border').hide();
                clearInterval(checkInterval);
            }
        });
    }, 2000);

    // 模态框关闭时清理定时器
    $('#taskProgressModal').on('hidden.bs.modal', function() {
        clearInterval(checkInterval);
        $(this).remove();
    });
}

function refreshRequirements() {
    currentPage = 1;
    currentSearch = '';
    currentTypeFilter = '';
    currentPriorityFilter = '';
    currentStatusFilter = '';
    currentDocumentFilter = '';
    $('#searchInput').val('');
    $('#typeFilter').val('');
    $('#priorityFilter').val('');
    $('#statusFilter').val('');
    $('#documentFilter').val('');
    loadRequirements();
}

// 为单个功能点生成测试用例
function generateTestCasesForRequirement(requirementId, requirementTitle) {
    $('#singleTestGenerationModal').modal('show');
    $('#singleRequirementId').val(requirementId);
    $('#singleRequirementTitle').text(requirementTitle);

    // 重置表单
    $('#singleTestTypes input[type="checkbox"]').prop('checked', false);
    $('#singleTestTypeUI').prop('checked', true);
    $('#singleUseKnowledgeBase').prop('checked', true);
}

// 查看功能点的测试用例
function viewTestCasesForRequirement(requirementId, requirementTitle) {
    $('#testCasesModal').modal('show');
    $('#testCasesModalTitle').text(`${requirementTitle} - 测试用例列表`);
    $('#testCasesRequirementId').val(requirementId);

    // 加载测试用例列表
    loadTestCasesForRequirement(requirementId);
}

// 加载指定功能点的测试用例
function loadTestCasesForRequirement(requirementId) {
    const tbody = $('#testCasesTableBody');
    tbody.html('<tr><td colspan="6" class="text-center">加载中...</td></tr>');

    $.get('/aitest/tests/api/list_by_requirement', {
        requirement_id: requirementId,
        page_size: 100
    }, function(response) {
        if (response.success) {
            renderTestCasesTable(response.data.records);
        } else {
            tbody.html('<tr><td colspan="6" class="text-center text-danger">加载失败</td></tr>');
        }
    }).fail(function() {
        tbody.html('<tr><td colspan="6" class="text-center text-danger">加载失败</td></tr>');
    });
}

// 渲染测试用例表格
function renderTestCasesTable(testCases) {
    const tbody = $('#testCasesTableBody');
    tbody.empty();

    if (testCases.length === 0) {
        tbody.append('<tr><td colspan="6" class="text-center text-muted">暂无测试用例</td></tr>');
        return;
    }

    testCases.forEach(function(testCase) {
        const testTypeBadge = getTestTypeBadge(testCase.test_type);
        const priorityBadge = getPriorityBadge(testCase.priority);
        const statusBadge = getStatusBadge(testCase.status);

        const row = $(`
            <tr>
                <td><strong>${testCase.name}</strong></td>
                <td>${testTypeBadge}</td>
                <td>${priorityBadge}</td>
                <td>${statusBadge}</td>
                <td><small class="text-muted">${new Date(testCase.created_at).toLocaleString()}</small></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewTestCaseDetail('${testCase.id}')" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 获取测试类型徽章
function getTestTypeBadge(testType) {
    const badges = {
        'functional': '<span class="badge bg-primary">功能测试</span>',
        'api': '<span class="badge bg-success">接口测试</span>',
        'system': '<span class="badge bg-warning">系统测试</span>',
        'performance': '<span class="badge bg-danger">性能测试</span>',
        'security': '<span class="badge bg-dark">安全测试</span>'
    };
    return badges[testType] || `<span class="badge bg-secondary">${testType}</span>`;
}

// 查看测试用例详情
function viewTestCaseDetail(testCaseId) {
    showUserTestCaseDetails(testCaseId);
}
function showUserTestCaseDetails(userTestCaseId) {
    // 获取用户测试用例详情
    $.get(`/aitest/tests/api/${userTestCaseId}`, function(response) {
        if (response.success) {
            const testCase = response.data;
            const modalContent = `
                <div class="modal fade" id="userTestCaseModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">用户测试用例详情</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>用例名称:</strong> ${testCase.name || '未命名'}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>状态:</strong> ${getStatusBadge(testCase.status)}
                                    </div>
                                </div>
                                <hr>
                                <div class="mb-3">
                                    <strong>描述:</strong><br>
                                    ${testCase.description || '暂无描述'}
                                </div>
                                <div class="mb-3">
                                    <strong>测试步骤:</strong><br>
                                    <pre class="bg-light p-2">${testCase.steps || '暂无步骤'}</pre>
                                </div>
                                <div class="mb-3">
                                    <strong>预期结果:</strong><br>
                                    <pre class="bg-light p-2">${testCase.expected_result || '暂无预期结果'}</pre>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            $('#userTestCaseModal').remove();

            // 添加新的模态框并显示
            $('body').append(modalContent);
            const modal = new bootstrap.Modal($('#userTestCaseModal')[0]);
            modal.show();
        } else {
            showNotification('获取用户测试用例详情失败', 'error');
        }
    }).fail(function() {
        showNotification('获取用户测试用例详情失败', 'error');
    });
}
</script>
{% endblock %}
