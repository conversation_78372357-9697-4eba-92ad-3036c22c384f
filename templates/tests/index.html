{% extends "base.html" %}

{% block title %}测试管理 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/aitest">首页</a></li>
<li class="breadcrumb-item active">测试管理</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-vial me-2"></i>测试管理
            </h1>
            <div class="btn-group">
                <button class="btn btn-primary" onclick="showGenerateModal()">
                    <i class="fas fa-magic me-1"></i>生成测试用例
                </button>
                <button class="btn btn-success" onclick="showCreateModal()">
                    <i class="fas fa-plus me-1"></i>创建测试用例
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="search-box">
            <input type="text" class="form-control" id="searchInput" placeholder="搜索测试用例...">
            <i class="fas fa-search search-icon"></i>
        </div>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="statusFilter">
            <option value="">所有状态</option>
            <option value="pending">待执行</option>
            <option value="running">执行中</option>
            <option value="passed">通过</option>
            <option value="failed">失败</option>
            <option value="skipped">跳过</option>
        </select>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="typeFilter">
            <option value="">所有类型</option>
            <option value="ui">UI测试</option>
            <option value="api">接口测试</option>
            <option value="system">系统测试</option>
            <option value="performance">性能测试</option>
            <option value="security">安全测试</option>
        </select>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="pageSizeSelect" onchange="changePageSize()">
            <option value="10" selected>10条</option>
            <option value="30">30条</option>
            <option value="100">100条</option>
        </select>
    </div>
    <div class="col-md-3 text-end">
        <button class="btn btn-outline-secondary" onclick="refreshTests()">
            <i class="fas fa-sync me-1"></i>刷新
        </button>
    </div>
</div>

<!-- 批量操作 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex align-items-center">
            <div class="btn-group" id="batchActions" style="display: none;">
                <button class="btn btn-sm btn-outline-primary" onclick="batchExecute()">
                    <i class="fas fa-play me-1"></i>批量执行
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="batchDelete()">
                    <i class="fas fa-trash me-1"></i>批量删除
                </button>
                <button class="btn btn-sm btn-outline-success" onclick="batchGenerateAutomation()">
                    <i class="fas fa-robot me-1"></i>批量生成自动化用例
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 测试用例列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="headerCheckbox" onchange="toggleSelectAll()">
                                </th>
                                <th>测试用例名称</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>关联需求</th>
                                <th>最后执行</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="testsTableBody">
                            <!-- 测试用例列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div id="pagination"></div>
            </div>
        </div>
    </div>
</div>

<!-- 生成测试用例模态框 -->
<div class="modal fade" id="generateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">生成测试用例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="generateForm">
                    <div class="mb-3">
                        <label for="requirementSelect" class="form-label">选择需求功能点</label>
                        <select class="form-select" id="requirementSelect" multiple size="8">
                            <!-- 需求列表将动态加载 -->
                        </select>
                        <div class="form-text">按住Ctrl键可多选</div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="testType" class="form-label">测试类型</label>
                                <div id="testTypeCheckboxes" class="d-flex flex-wrap gap-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="ui" id="testTypeUI" checked>
                                        <label class="form-check-label" for="testTypeUI">UI测试</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="api" id="testTypeAPI">
                                        <label class="form-check-label" for="testTypeAPI">接口测试</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="system" id="testTypeSystem">
                                        <label class="form-check-label" for="testTypeSystem">系统测试</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="performance" id="testTypePerformance">
                                        <label class="form-check-label" for="testTypePerformance">性能测试</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="security" id="testTypeSecurity">
                                        <label class="form-check-label" for="testTypeSecurity">安全测试</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="generateThreads" class="form-label">并发线程数</label>
                                <input type="number" class="form-control" id="generateThreads" min="1" max="10" value="4">
                                <div class="form-text">用于并发生成测试用例</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check" style="margin-top: 32px;">
                                    <input class="form-check-input" type="checkbox" id="includeAutomation" checked>
                                    <label class="form-check-label" for="includeAutomation">
                                        生成自动化测试用例
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="generateTestCases()">开始生成</button>
            </div>
        </div>
    </div>
</div>

<!-- 需求详情模态框 -->
<div class="modal fade" id="requirementDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">需求详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="requirementDetailContent">
                    <!-- 需求详情内容将通过JavaScript动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/aitest/static/js/test_mananger.js"></script>
{% endblock %}